package com.gofar.user_service.service;

import com.gofar.user_service.dto.*;
import com.gofar.user_service.entity.EcoUser;
import com.gofar.user_service.entity.EnergyPreference;
import com.gofar.user_service.entity.RoleEntity;
import com.gofar.user_service.repository.EcoUserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class EcoUserService {

    private final EcoUserRepository ecoUserRepository;
    private final RoleService roleService;
    private PasswordEncoder passwordEncoder;

    public EcoUserService(EcoUserRepository ecoUserRepository, RoleService roleService) {
        this.ecoUserRepository = ecoUserRepository;
        this.roleService = roleService;
    }

    public Optional<EcoUserResponseDto> findByEmail(String email) {
        return ecoUserRepository.findByEmail(email)
                .map(user -> new EcoUserResponseDto(user.getId(),
                        user.getEmail(), user.getFirstName(), user.getLastName(), convertRoleToDto(user.getRole()), user.getPreference()));
    }

    public EcoUserResponseDto save(EcoUserCreationDto ecoUserDto) {
        if (ecoUserRepository.existsByEmail(ecoUserDto.getEmail())) {
            throw new IllegalArgumentException("Email already exists");
        }

        // Find the role by name
        RoleEntity role = roleService.findByName(ecoUserDto.getRoleName())
                .orElseThrow(() -> new IllegalArgumentException("Role not found: " + ecoUserDto.getRoleName()));

        EcoUser user = new EcoUser();
        user.setFirstName(ecoUserDto.getFirstName());
        user.setLastName(ecoUserDto.getLastName());
        user.setEmail(ecoUserDto.getEmail());
        user.setPassword(passwordEncoder.encode(ecoUserDto.getPassword()));
        user.setRole(role);
        user.setPreference(ecoUserDto.getPreference());
        ecoUserRepository.save(user);
        return new EcoUserResponseDto(user.getId(),
                user.getEmail(), user.getFirstName(), user.getLastName(), convertRoleToDto(user.getRole()), user.getPreference());
    }

    public ValidEcoUserDetails validateUser(String email, String password) {
        Optional<EcoUser> userOptional = ecoUserRepository.findByEmail(email);
        if (userOptional.isPresent() && passwordEncoder.matches(password, userOptional.get().getPassword())) {
            EcoUser user = userOptional.get();
            RoleEntity role = user.getRole();

            // Get all active permission names as scopes
            List<String> scopes = new ArrayList<>(role.getActivePermissionNames());

            return new ValidEcoUserDetails(user.getEmail(), role.getName(), scopes);
        }
        return null;
    }

    public void updateProfile(ProfileUpdateDto updateDto) {
        EcoUser currentUser = getCurrentUser();
        currentUser.setFirstName(updateDto.firstName());
        currentUser.setLastName(updateDto.lastName());
        ecoUserRepository.saveAndFlush(currentUser);
    }

    public void updatePassword(String oldPassword, String newPassword) {
        EcoUser currentUser = getCurrentUser();
        if (passwordEncoder.matches(oldPassword, currentUser.getPassword())) {
            currentUser.setPassword(passwordEncoder.encode(newPassword));
            ecoUserRepository.saveAndFlush(currentUser);
        }
        throw new IllegalArgumentException("Invalid old password");
    }

    public void updatePreferences(PreferencesDto preferencesDto) {
        EcoUser currentUser = getCurrentUser();
        currentUser.setPreference(new EnergyPreference(preferencesDto.priorityEco(), preferencesDto.EVCharging(), preferencesDto.preferredTime()));
        ecoUserRepository.saveAndFlush(currentUser);
    }


    private EcoUser getCurrentUser() {
        return ecoUserRepository.findByEmail(getUserEmail()).orElseThrow(() -> new IllegalArgumentException("User not found"));
    }

    private String getUserEmail() {
        return SecurityContextHolder.getContext().getAuthentication().getName();
    }

    @Autowired
    public void setPasswordEncoder(PasswordEncoder passwordEncoder) {
        this.passwordEncoder = passwordEncoder;
    }

    private RoleDto convertRoleToDto(RoleEntity role) {
        RoleDto dto = new RoleDto();
        dto.setId(role.getId());
        dto.setName(role.getName());
        dto.setDescription(role.getDescription());
        dto.setActive(role.isActive());

        // Convert permissions to DTOs
        dto.setPermissions(role.getPermissions().stream()
                .map(permission -> new PermissionDto(
                        permission.getId(),
                        permission.getName(),
                        permission.getDescription(),
                        permission.getCategory(),
                        permission.isActive()
                ))
                .collect(java.util.stream.Collectors.toSet()));

        return dto;
    }
}
