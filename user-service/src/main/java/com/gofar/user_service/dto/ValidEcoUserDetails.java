package com.gofar.user_service.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * This class is used to encapsulate the user details that are returned to
 * the auth server after a successful authentication.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ValidEcoUserDetails {
    private String email;
    private String roleName;
    private List<String> scopes;
}
