package com.gofar.user_service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Role data transfer object")
public class RoleDto {

    @Schema(description = "Role unique identifier", example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @NotBlank(message = "Role name is mandatory")
    @Size(max = 50, message = "Role name must not exceed 50 characters")
    @Schema(description = "Role name", example = "ADMIN")
    private String name;

    @Size(max = 255, message = "Role description must not exceed 255 characters")
    @Schema(description = "Role description", example = "Administrator role with full system access")
    private String description;

    @Schema(description = "Whether the role is active", example = "true")
    private boolean active = true;

    @Schema(description = "Set of permissions assigned to this role")
    private Set<PermissionDto> permissions = new HashSet<>();

    public RoleDto(String name, String description) {
        this.name = name;
        this.description = description;
        this.permissions = new HashSet<>();
    }
}
