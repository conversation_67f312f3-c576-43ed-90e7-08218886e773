package com.gofar.user_service.controller;

import com.gofar.user_service.dto.PermissionDto;
import com.gofar.user_service.entity.Permission;
import com.gofar.user_service.service.PermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/permissions")
@Tag(name = "Permission Management", description = "APIs for managing system permissions")
@SecurityRequirement(name = "Bearer Token")
public class PermissionController {

    private final PermissionService permissionService;

    public PermissionController(PermissionService permissionService) {
        this.permissionService = permissionService;
    }

    @GetMapping
    @Operation(summary = "Get all active permissions", description = "Retrieve all active permissions in the system")
    @PreAuthorize("hasRole('PERMISSION_READ')")
    public ResponseEntity<List<PermissionDto>> getAllPermissions() {
        List<Permission> permissions = permissionService.getAllActivePermissions();
        List<PermissionDto> permissionDtos = permissions.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(permissionDtos);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get permission by ID", description = "Retrieve a specific permission by its ID")
    @PreAuthorize("hasRole('PERMISSION_READ')")
    public ResponseEntity<PermissionDto> getPermissionById(@PathVariable UUID id) {
        return permissionService.findById(id)
                .map(permission -> ResponseEntity.ok(convertToDto(permission)))
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/search")
    @Operation(summary = "Search permissions by name", description = "Search permissions by name (case insensitive)")
    @PreAuthorize("hasRole('PERMISSION_READ')")
    public ResponseEntity<List<PermissionDto>> searchPermissions(@RequestParam String name) {
        List<Permission> permissions = permissionService.searchPermissionsByName(name);
        List<PermissionDto> permissionDtos = permissions.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(permissionDtos);
    }

    @GetMapping("/categories")
    @Operation(summary = "Get all permission categories", description = "Retrieve all distinct permission categories")
    @PreAuthorize("hasRole('PERMISSION_READ')")
    public ResponseEntity<List<String>> getCategories() {
        List<String> categories = permissionService.getAllCategories();
        return ResponseEntity.ok(categories);
    }

    @GetMapping("/category/{category}")
    @Operation(summary = "Get permissions by category", description = "Retrieve all permissions in a specific category")
    @PreAuthorize("hasRole('PERMISSION_READ')")
    public ResponseEntity<List<PermissionDto>> getPermissionsByCategory(@PathVariable String category) {
        List<Permission> permissions = permissionService.getPermissionsByCategory(category);
        List<PermissionDto> permissionDtos = permissions.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(permissionDtos);
    }

    @PostMapping
    @Operation(summary = "Create new permission", description = "Create a new permission in the system")
    @PreAuthorize("hasRole('PERMISSION_CREATE')")
    public ResponseEntity<PermissionDto> createPermission(@RequestBody @Valid PermissionDto permissionDto) {
        Permission permission = permissionService.createPermission(
                permissionDto.getName(),
                permissionDto.getDescription(),
                permissionDto.getCategory()
        );
        return ResponseEntity.status(HttpStatus.CREATED).body(convertToDto(permission));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update permission", description = "Update an existing permission")
    @PreAuthorize("hasRole('PERMISSION_UPDATE')")
    public ResponseEntity<PermissionDto> updatePermission(
            @PathVariable UUID id,
            @RequestBody @Valid PermissionDto permissionDto) {
        try {
            Permission permission = permissionService.updatePermission(
                    id,
                    permissionDto.getName(),
                    permissionDto.getDescription(),
                    permissionDto.getCategory()
            );
            return ResponseEntity.ok(convertToDto(permission));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/{id}/activate")
    @Operation(summary = "Activate permission", description = "Activate a deactivated permission")
    @PreAuthorize("hasRole('PERMISSION_UPDATE')")
    public ResponseEntity<PermissionDto> activatePermission(@PathVariable UUID id) {
        try {
            Permission permission = permissionService.activatePermission(id);
            return ResponseEntity.ok(convertToDto(permission));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/{id}/deactivate")
    @Operation(summary = "Deactivate permission", description = "Deactivate a permission (soft delete)")
    @PreAuthorize("hasRole('PERMISSION_UPDATE')")
    public ResponseEntity<PermissionDto> deactivatePermission(@PathVariable UUID id) {
        try {
            Permission permission = permissionService.deactivatePermission(id);
            return ResponseEntity.ok(convertToDto(permission));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete permission", description = "Delete a permission (soft delete by deactivating)")
    @PreAuthorize("hasRole('PERMISSION_DELETE')")
    public ResponseEntity<Void> deletePermission(@PathVariable UUID id) {
        try {
            permissionService.deletePermission(id);
            return ResponseEntity.noContent().build();
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    private PermissionDto convertToDto(Permission permission) {
        return new PermissionDto(
                permission.getId(),
                permission.getName(),
                permission.getDescription(),
                permission.getCategory(),
                permission.isActive()
        );
    }
}
