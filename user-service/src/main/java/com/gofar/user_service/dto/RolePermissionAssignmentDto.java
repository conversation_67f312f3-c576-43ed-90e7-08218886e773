package com.gofar.user_service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "DTO for assigning/removing permissions to/from roles")
public class RolePermissionAssignmentDto {

    @NotNull(message = "Role ID is mandatory")
    @Schema(description = "Role unique identifier", example = "123e4567-e89b-12d3-a456-************")
    private UUID roleId;

    @NotNull(message = "Permission ID is mandatory")
    @Schema(description = "Permission unique identifier", example = "123e4567-e89b-12d3-a456-************")
    private UUID permissionId;
}
