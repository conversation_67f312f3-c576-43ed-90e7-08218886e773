package com.gofar.user_service.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

/**
 * Entity representing a role in the system.
 * Roles contain multiple permissions that define what actions users with this role can perform.
 */
@Entity
@Table(name = "ROLE", indexes = {
    @Index(columnList = "name", name = "ROLE_NAME_IDX")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(name = "name", unique = true, nullable = false, length = 50)
    private String name;

    @Column(name = "description", length = 255)
    private String description;

    @Column(name = "active", nullable = false)
    private boolean active = true;

    @ManyToMany(fetch = FetchType.EAGER, cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
        name = "ROLE_PERMISSION",
        joinColumns = @JoinColumn(name = "role_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id"),
        indexes = {
            @Index(columnList = "role_id", name = "ROLE_PERMISSION_ROLE_IDX"),
            @Index(columnList = "permission_id", name = "ROLE_PERMISSION_PERMISSION_IDX")
        }
    )
    private Set<Permission> permissions = new HashSet<>();

    public RoleEntity(String name, String description) {
        this.name = name;
        this.description = description;
        this.permissions = new HashSet<>();
    }

    /**
     * Add a permission to this role
     */
    public void addPermission(Permission permission) {
        this.permissions.add(permission);
    }

    /**
     * Remove a permission from this role
     */
    public void removePermission(Permission permission) {
        this.permissions.remove(permission);
    }

    /**
     * Check if this role has a specific permission
     */
    public boolean hasPermission(String permissionName) {
        return permissions.stream()
                .anyMatch(permission -> permission.getName().equals(permissionName) && permission.isActive());
    }

    /**
     * Get all active permission names for this role
     */
    public Set<String> getActivePermissionNames() {
        return permissions.stream()
                .filter(Permission::isActive)
                .map(Permission::getName)
                .collect(java.util.stream.Collectors.toSet());
    }
}
