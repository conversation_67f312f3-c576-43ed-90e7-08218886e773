package com.gofar.user_service.service;

import com.gofar.user_service.entity.Permission;
import com.gofar.user_service.entity.RoleEntity;
import com.gofar.user_service.repository.RoleRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

@Service
@Transactional
public class RoleService {

    private final RoleRepository roleRepository;
    private final PermissionService permissionService;

    public RoleService(RoleRepository roleRepository, PermissionService permissionService) {
        this.roleRepository = roleRepository;
        this.permissionService = permissionService;
    }

    /**
     * Create a new role
     */
    public RoleEntity createRole(String name, String description) {
        if (roleRepository.existsByName(name)) {
            throw new IllegalArgumentException("Role with name '" + name + "' already exists");
        }
        
        RoleEntity role = new RoleEntity(name, description);
        return roleRepository.save(role);
    }

    /**
     * Update an existing role
     */
    public RoleEntity updateRole(UUID id, String name, String description) {
        RoleEntity role = roleRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Role not found with id: " + id));

        // Check if the name is being changed and if new name already exists
        if (!role.getName().equals(name) && roleRepository.existsByName(name)) {
            throw new IllegalArgumentException("Role with name '" + name + "' already exists");
        }

        role.setName(name);
        role.setDescription(description);
        
        return roleRepository.save(role);
    }

    /**
     * Find a role by ID
     */
    @Transactional(readOnly = true)
    public Optional<RoleEntity> findById(UUID id) {
        return roleRepository.findById(id);
    }

    /**
     * Find a role by name
     */
    @Transactional(readOnly = true)
    public Optional<RoleEntity> findByName(String name) {
        return roleRepository.findByName(name);
    }

    /**
     * Get all active roles
     */
    @Transactional(readOnly = true)
    public List<RoleEntity> getAllActiveRoles() {
        return roleRepository.findByActiveTrue();
    }

    /**
     * Add permission to a role
     */
    public RoleEntity addPermissionToRole(UUID roleId, UUID permissionId) {
        RoleEntity role = roleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException("Role not found with id: " + roleId));
        
        Permission permission = permissionService.findById(permissionId)
                .orElseThrow(() -> new IllegalArgumentException("Permission not found with id: " + permissionId));

        role.addPermission(permission);
        return roleRepository.saveAndFlush(role);
    }

    /**
     * Add permission to a role by names
     */
    public RoleEntity addPermissionToRole(String roleName, String permissionName) {
        RoleEntity role = roleRepository.findByName(roleName)
                .orElseThrow(() -> new IllegalArgumentException("Role not found with name: " + roleName));
        
        Permission permission = permissionService.findByName(permissionName)
                .orElseThrow(() -> new IllegalArgumentException("Permission not found with name: " + permissionName));

        role.addPermission(permission);
        return roleRepository.saveAndFlush(role);
    }

    /**
     * Remove permission from a role
     */
    public RoleEntity removePermissionFromRole(UUID roleId, UUID permissionId) {
        RoleEntity role = roleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException("Role not found with id: " + roleId));
        
        Permission permission = permissionService.findById(permissionId)
                .orElseThrow(() -> new IllegalArgumentException("Permission not found with id: " + permissionId));

        role.removePermission(permission);
        return roleRepository.save(role);
    }

    /**
     * Remove permission from a role by names
     */
    public RoleEntity removePermissionFromRole(String roleName, String permissionName) {
        RoleEntity role = roleRepository.findByName(roleName)
                .orElseThrow(() -> new IllegalArgumentException("Role not found with name: " + roleName));
        
        Permission permission = permissionService.findByName(permissionName)
                .orElseThrow(() -> new IllegalArgumentException("Permission not found with name: " + permissionName));

        role.removePermission(permission);
        return roleRepository.save(role);
    }

    /**
     * Get all permissions for a role
     */
    @Transactional(readOnly = true)
    public Set<Permission> getRolePermissions(UUID roleId) {
        RoleEntity role = roleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException("Role not found with id: " + roleId));
        
        return role.getPermissions();
    }

    /**
     * Get all active permission names for a role
     */
    @Transactional(readOnly = true)
    public Set<String> getRolePermissionNames(UUID roleId) {
        RoleEntity role = roleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException("Role not found with id: " + roleId));
        
        return role.getActivePermissionNames();
    }

    /**
     * Check if a role has permission
     */
    @Transactional(readOnly = true)
    public boolean roleHasPermission(UUID roleId, String permissionName) {
        RoleEntity role = roleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException("Role not found with id: " + roleId));
        
        return role.hasPermission(permissionName);
    }

    /**
     * Activate a role
     */
    public RoleEntity activateRole(UUID id) {
        RoleEntity role = roleRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Role not found with id: " + id));
        
        role.setActive(true);
        return roleRepository.save(role);
    }

    /**
     * Deactivate a role
     */
    public RoleEntity deactivateRole(UUID id) {
        RoleEntity role = roleRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Role not found with id: " + id));
        
        role.setActive(false);
        return roleRepository.save(role);
    }

    /**
     * Search roles by name
     */
    @Transactional(readOnly = true)
    public List<RoleEntity> searchRolesByName(String name) {
        return roleRepository.findByNameContainingIgnoreCase(name);
    }

    /**
     * Get all roles (including inactive)
     */
    @Transactional(readOnly = true)
    public List<RoleEntity> getAllRoles() {
        return roleRepository.findAll();
    }
}
