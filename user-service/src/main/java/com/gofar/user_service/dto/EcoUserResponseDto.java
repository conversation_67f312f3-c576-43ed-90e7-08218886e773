package com.gofar.user_service.dto;

import com.gofar.user_service.entity.EnergyPreference;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class EcoUserResponseDto {

    private UUID id;
    private String email;
    private String firstName;
    private String lastName;
    private RoleDto role;
    private EnergyPreference preference;
}
