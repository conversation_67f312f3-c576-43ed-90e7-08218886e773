package com.gofar.user_service.service;

import com.gofar.user_service.entity.Permission;
import com.gofar.user_service.repository.PermissionRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional
public class PermissionService {

    private final PermissionRepository permissionRepository;

    public PermissionService(PermissionRepository permissionRepository) {
        this.permissionRepository = permissionRepository;
    }

    /**
     * Create a new permission
     */
    public Permission createPermission(String name, String description, String category) {
        if (permissionRepository.existsByName(name)) {
            throw new IllegalArgumentException("Permission with name '" + name + "' already exists");
        }
        
        Permission permission = new Permission(name, description, category);
        return permissionRepository.save(permission);
    }

    /**
     * Update an existing permission
     */
    public Permission updatePermission(UUID id, String name, String description, String category) {
        Permission permission = permissionRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Permission not found with id: " + id));

        // Check if the name is being changed and if new name already exists
        if (!permission.getName().equals(name) && permissionRepository.existsByName(name)) {
            throw new IllegalArgumentException("Permission with name '" + name + "' already exists");
        }

        permission.setName(name);
        permission.setDescription(description);
        permission.setCategory(category);
        
        return permissionRepository.saveAndFlush(permission);
    }

    /**
     * Find permission by ID
     */
    @Transactional(readOnly = true)
    public Optional<Permission> findById(UUID id) {
        return permissionRepository.findById(id);
    }

    /**
     * Find permission by name
     */
    @Transactional(readOnly = true)
    public Optional<Permission> findByName(String name) {
        return permissionRepository.findByName(name);
    }

    /**
     * Get all active permissions
     */
    @Transactional(readOnly = true)
    public List<Permission> getAllActivePermissions() {
        return permissionRepository.findByActiveTrue();
    }

    /**
     * Get permissions by category
     */
    @Transactional(readOnly = true)
    public List<Permission> getPermissionsByCategory(String category) {
        return permissionRepository.findByCategoryAndActiveTrue(category);
    }

    /**
     * Get all distinct categories
     */
    @Transactional(readOnly = true)
    public List<String> getAllCategories() {
        return permissionRepository.findDistinctCategories();
    }

    /**
     * Search permissions by name
     */
    @Transactional(readOnly = true)
    public List<Permission> searchPermissionsByName(String name) {
        return permissionRepository.findByNameContainingIgnoreCase(name);
    }

    /**
     * Activate a permission
     */
    public Permission activatePermission(UUID id) {
        Permission permission = permissionRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Permission not found with id: " + id));
        
        permission.setActive(true);
        return permissionRepository.save(permission);
    }

    /**
     * Deactivate a permission
     */
    public Permission deactivatePermission(UUID id) {
        Permission permission = permissionRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Permission not found with id: " + id));
        
        permission.setActive(false);
        return permissionRepository.save(permission);
    }

    /**
     * Delete a permission (soft delete by deactivating)
     */
    public void deletePermission(UUID id) {
        deactivatePermission(id);
    }

    /**
     * Get all permissions (including inactive)
     */
    @Transactional(readOnly = true)
    public List<Permission> getAllPermissions() {
        return permissionRepository.findAll();
    }
}
