package com.gofar.user_service.utils;

import java.util.Map;

/**
 * Constants class for common constants used across the application
 */
public class Constants {


    public static final String DEFAULT_ADMIN_EMAIL = "<EMAIL>";
    public static final String DEFAULT_ADMIN_PASSWORD = "admin";
    public static final String ADMIN = "ADMIN";
    public static final String USER = "USER";
    public static final String TECHNICIAN = "TECHNICIAN";
    public static final String ADMIN_ROLE_DESCRIPTION = "Administrator role with full system access";
    public static final String USER_ROLE_DESCRIPTION = "Standard user role";
    public static final String TECHNICIAN_ROLE_DESCRIPTION = "Technician role for energy management";

    public static final Map<String, String> ROLE_DESCRIPTIONS = Map.of(
        ADMIN, ADMIN_ROLE_DESCRIPTION,
        USER, USER_ROLE_DESCRIPTION,
        TECHNICIAN, TECHNICIAN_ROLE_DESCRIPTION
    );

    public static final String[] ROLES = {ADMIN, USER, TECHNICIAN};

    public static final String PERMISSION_READ = "PERMISSION_READ";
    public static final String PERMISSION_CREATE = "PERMISSION_CREATE";
    public static final String PERMISSION_UPDATE = "PERMISSION_UPDATE";
    public static final String PERMISSION_DELETE = "PERMISSION_DELETE";
    public static final String ROLE_READ = "ROLE_READ";
    public static final String ROLE_CREATE = "ROLE_CREATE";
    public static final String ROLE_UPDATE = "ROLE_UPDATE";
    public static final String ROLE_DELETE = "ROLE_DELETE";
    public static final String PROFILE_READ = "PROFILE_READ";
    public static final String PROFILE_UPDATE = "PROFILE_UPDATE";
    public static final String PREFERENCES_UPDATE = "PREFERENCES_UPDATE";
    public static final String ENERGY_READ = "ENERGY_READ";
    public static final String ENERGY_WRITE = "ENERGY_WRITE";
    public static final String ENERGY_CONTROL = "ENERGY_CONTROL";
    public static final String SYSTEM_MONITOR = "SYSTEM_MONITOR";
    public static final String SYSTEM_CONFIG = "SYSTEM_CONFIG";
    public static final String SYSTEM_BACKUP = "SYSTEM_BACKUP";
    public static final String USER_READ = "USER_READ";
    public static final String USER_CREATE = "USER_CREATE";
    public static final String USER_UPDATE = "USER_UPDATE";
    public static final String USER_DELETE = "USER_DELETE";

    public static final String[] USER_PERMISSIONS = {
        PROFILE_READ, PROFILE_UPDATE, PREFERENCES_UPDATE
    };

    public static final String[] TECHNICIAN_PERMISSIONS = {
        ENERGY_READ, ENERGY_WRITE, ENERGY_CONTROL, SYSTEM_MONITOR,
        PROFILE_READ, PROFILE_UPDATE, PREFERENCES_UPDATE
    };

    public static final String[] ALL_PERMISSIONS = {
        PERMISSION_READ, PERMISSION_CREATE, PERMISSION_UPDATE, PERMISSION_DELETE,
        ROLE_READ, ROLE_CREATE, ROLE_UPDATE, ROLE_DELETE,
        PROFILE_READ, PROFILE_UPDATE, PREFERENCES_UPDATE,
        ENERGY_READ, ENERGY_WRITE, ENERGY_CONTROL,
        SYSTEM_MONITOR, SYSTEM_CONFIG, SYSTEM_BACKUP,
        USER_READ, USER_CREATE, USER_UPDATE, USER_DELETE
    };

    public static final Map<String, String[]> ROLE_PERMISSIONS = Map.of(
        ADMIN, ALL_PERMISSIONS,
        USER, USER_PERMISSIONS,
        TECHNICIAN, TECHNICIAN_PERMISSIONS
    );

    public static final String USER_READ_DESCRIPTION = "Read user information";
    public static final String USER_CREATE_DESCRIPTION = "Create new users";
    public static final String USER_UPDATE_DESCRIPTION = "Update user information";
    public static final String USER_DELETE_DESCRIPTION = "Delete users";
    public static final String ROLE_READ_DESCRIPTION = "Read role information";
    public static final String ROLE_CREATE_DESCRIPTION = "Create new roles";
    public static final String ROLE_UPDATE_DESCRIPTION = "Update role information";
    public static final String ROLE_DELETE_DESCRIPTION = "Delete roles";
    public static final String PERMISSION_READ_DESCRIPTION = "Read permission information";
    public static final String PERMISSION_CREATE_DESCRIPTION = "Create new permissions";
    public static final String PERMISSION_UPDATE_DESCRIPTION = "Update permission information";
    public static final String PERMISSION_DELETE_DESCRIPTION = "Delete permissions";
    public static final String ENERGY_READ_DESCRIPTION = "Read energy data";
    public static final String ENERGY_WRITE_DESCRIPTION = "Modify energy settings";
    public static final String ENERGY_CONTROL_DESCRIPTION = "Control energy devices";
    public static final String SYSTEM_MONITOR_DESCRIPTION = "Monitor system health";
    public static final String SYSTEM_CONFIG_DESCRIPTION = "Configure system settings";
    public static final String SYSTEM_BACKUP_DESCRIPTION = "Perform system backups";
    public static final String PROFILE_READ_DESCRIPTION = "Read own profile";
    public static final String PROFILE_UPDATE_DESCRIPTION = "Update own profile";
    public static final String PREFERENCES_UPDATE_DESCRIPTION = "Update energy preferences";

    public static final String USER_MANAGEMENT_CATEGORY = "USER_MANAGEMENT";
    public static final String ROLE_MANAGEMENT_CATEGORY = "ROLE_MANAGEMENT";
    public static final String PERMISSION_MANAGEMENT_CATEGORY = "PERMISSION_MANAGEMENT";
    public static final String ENERGY_MANAGEMENT_CATEGORY = "ENERGY_MANAGEMENT";
    public static final String SYSTEM_ADMINISTRATION_CATEGORY = "SYSTEM_ADMINISTRATION";
    public static final String USER_PROFILE_CATEGORY = "USER_PROFILE";

    public static final Map<String, Map<String, String>> PERMISSION_DESCRIPTIONS = Map.of(
            USER_MANAGEMENT_CATEGORY, Map.of(
                    USER_READ, USER_READ_DESCRIPTION,
                    USER_CREATE, USER_CREATE_DESCRIPTION,
                    USER_UPDATE, USER_UPDATE_DESCRIPTION,
                    USER_DELETE, USER_DELETE_DESCRIPTION
            ),
            ROLE_MANAGEMENT_CATEGORY, Map.of(
                    ROLE_READ, ROLE_READ_DESCRIPTION,
                    ROLE_CREATE, ROLE_CREATE_DESCRIPTION,
                    ROLE_UPDATE, ROLE_UPDATE_DESCRIPTION,
                    ROLE_DELETE, ROLE_DELETE_DESCRIPTION
            ),
            PERMISSION_MANAGEMENT_CATEGORY, Map.of(
                    PERMISSION_READ, PERMISSION_READ_DESCRIPTION,
                    PERMISSION_CREATE, PERMISSION_CREATE_DESCRIPTION,
                    PERMISSION_UPDATE, PERMISSION_UPDATE_DESCRIPTION,
                    PERMISSION_DELETE, PERMISSION_DELETE_DESCRIPTION
            ),
            ENERGY_MANAGEMENT_CATEGORY, Map.of(
                    ENERGY_READ, ENERGY_READ_DESCRIPTION,
                    ENERGY_WRITE, ENERGY_WRITE_DESCRIPTION,
                    ENERGY_CONTROL, ENERGY_CONTROL_DESCRIPTION
            ),
            SYSTEM_ADMINISTRATION_CATEGORY, Map.of(
                    SYSTEM_MONITOR, SYSTEM_MONITOR_DESCRIPTION,
                    SYSTEM_CONFIG, SYSTEM_CONFIG_DESCRIPTION,
                    SYSTEM_BACKUP, SYSTEM_BACKUP_DESCRIPTION
            ),
            USER_PROFILE_CATEGORY, Map.of(
                    PROFILE_READ, PROFILE_READ_DESCRIPTION,
                    PROFILE_UPDATE, PROFILE_UPDATE_DESCRIPTION,
                    PREFERENCES_UPDATE, PREFERENCES_UPDATE_DESCRIPTION
            )
    );
}
