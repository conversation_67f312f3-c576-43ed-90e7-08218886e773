package com.gofar.user_service.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * Entity representing a permission in the system.
 * Permissions define what actions a user can perform.
 */
@Entity
@Table(name = "PERMISSION", indexes = {
    @Index(columnList = "name", name = "PERMISSION_NAME_IDX"),
    @Index(columnList = "category", name = "PERMISSION_CATEGORY_IDX")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Permission {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;

    @Column(name = "name", unique = true, nullable = false, length = 100)
    private String name;

    @Column(name = "description", length = 255)
    private String description;

    @Column(name = "category", length = 50)
    private String category;

    @Column(name = "active", nullable = false)
    private boolean active = true;

    public Permission(String name, String description, String category) {
        this.name = name;
        this.description = description;
        this.category = category;
    }
}
