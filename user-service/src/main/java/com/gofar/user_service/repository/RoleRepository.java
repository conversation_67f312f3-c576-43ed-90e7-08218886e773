package com.gofar.user_service.repository;

import com.gofar.user_service.entity.RoleEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface RoleRepository extends JpaRepository<RoleEntity, UUID> {

    /**
     * Find a role by name
     */
    Optional<RoleEntity> findByName(String name);

    /**
     * Find all active roles
     */
    List<RoleEntity> findByActiveTrue();

    /**
     * Check if a role exists by name
     */
    boolean existsByName(String name);

    /**
     * Find roles with specific permission
     */
    @Query("SELECT r FROM RoleEntity r JOIN r.permissions p WHERE p.name = :permissionName AND r.active = true")
    List<RoleEntity> findByPermissionName(@Param("permissionName") String permissionName);

    /**
     * Find roles by name containing (case-insensitive)
     */
    @Query("SELECT r FROM RoleEntity r WHERE LOWER(r.name) LIKE LOWER(CONCAT('%', :name, '%')) AND r.active = true")
    List<RoleEntity> findByNameContainingIgnoreCase(@Param("name") String name);
}
