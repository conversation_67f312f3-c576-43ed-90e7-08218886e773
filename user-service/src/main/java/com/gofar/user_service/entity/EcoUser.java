package com.gofar.user_service.entity;


import com.gofar.user_service.security.CryptoConverter;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Entity
@Table(name = "ECUSR", indexes = @Index(columnList = "USRML", name = "USRML_IDX"))
@Data
@NoArgsConstructor
public class EcoUser {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private UUID id;
    @Column(name = "USRML", unique = true)
    @Convert(converter = CryptoConverter.class)
    private String email;
    @Column(name = "USRPWD")
    private String password;
    @Enumerated(EnumType.STRING)
    private Role role;
    @Column(name = "USRFNM")
    private String firstName;
    @Column(name = "USRLNM")
    private String lastName;
    @Embedded
    @AttributeOverrides({
            @AttributeOverride(name = "priorityEco", column = @Column(name = "USRPFEC")),
            @AttributeOverride(name = "EVCharging", column = @Column(name = "USRPFEVC")),
            @AttributeOverride(name = "preferredTime", column = @Column(name = "USRPFTM"))
    })
    private EnergyPreference preference;
}
