package com.gofar.user_service.controller;

import com.gofar.user_service.dto.PermissionDto;
import com.gofar.user_service.dto.RoleDto;
import com.gofar.user_service.dto.RolePermissionAssignmentDto;
import com.gofar.user_service.entity.Permission;
import com.gofar.user_service.entity.RoleEntity;
import com.gofar.user_service.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/roles")
@Tag(name = "Role Management", description = "APIs for managing system roles and role-permission assignments")
@SecurityRequirement(name = "Bearer Token")
public class RoleController {

    private final RoleService roleService;

    public RoleController(RoleService roleService) {
        this.roleService = roleService;
    }

    @GetMapping
    @Operation(summary = "Get all active roles", description = "Retrieve all active roles in the system")
    @PreAuthorize("hasAuthority('ROLE_ROLE_READ')")
    public ResponseEntity<List<RoleDto>> getAllRoles() {
        List<RoleEntity> roles = roleService.getAllActiveRoles();
        List<RoleDto> roleDtos = roles.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(roleDtos);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get role by ID", description = "Retrieve a specific role by its ID")
    @PreAuthorize("hasAuthority('ROLE_ROLE_READ')")
    public ResponseEntity<RoleDto> getRoleById(@PathVariable UUID id) {
        return roleService.findById(id)
                .map(role -> ResponseEntity.ok(convertToDto(role)))
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/search")
    @Operation(summary = "Search roles by name", description = "Search roles by name (case insensitive)")
    @PreAuthorize("hasAuthority('ROLE_ROLE_READ')")
    public ResponseEntity<List<RoleDto>> searchRoles(@RequestParam String name) {
        List<RoleEntity> roles = roleService.searchRolesByName(name);
        List<RoleDto> roleDtos = roles.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        return ResponseEntity.ok(roleDtos);
    }

    @PostMapping
    @Operation(summary = "Create new role", description = "Create a new role in the system")
    @PreAuthorize("hasAuthority('ROLE_ROLE_CREATE')")
    public ResponseEntity<RoleDto> createRole(@RequestBody @Valid RoleDto roleDto) {
        RoleEntity role = roleService.createRole(roleDto.getName(), roleDto.getDescription());
        return ResponseEntity.status(HttpStatus.CREATED).body(convertToDto(role));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update role", description = "Update an existing role")
    @PreAuthorize("hasAuthority('ROLE_ROLE_UPDATE')")
    public ResponseEntity<RoleDto> updateRole(@PathVariable UUID id, @RequestBody @Valid RoleDto roleDto) {
        try {
            RoleEntity role = roleService.updateRole(id, roleDto.getName(), roleDto.getDescription());
            return ResponseEntity.ok(convertToDto(role));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/{id}/permissions")
    @Operation(summary = "Get role permissions", description = "Get all permissions assigned to a role")
    @PreAuthorize("hasAuthority('ROLE_ROLE_READ')")
    public ResponseEntity<Set<PermissionDto>> getRolePermissions(@PathVariable UUID id) {
        try {
            Set<Permission> permissions = roleService.getRolePermissions(id);
            Set<PermissionDto> permissionDtos = permissions.stream()
                    .map(this::convertPermissionToDto)
                    .collect(Collectors.toSet());
            return ResponseEntity.ok(permissionDtos);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping("/permissions/assign")
    @Operation(summary = "Assign permission to role", description = "Assign a permission to a role")
    @PreAuthorize("hasAuthority('ROLE_ROLE_UPDATE')")
    public ResponseEntity<RoleDto> assignPermissionToRole(@RequestBody @Valid RolePermissionAssignmentDto assignment) {
        try {
            RoleEntity role = roleService.addPermissionToRole(assignment.getRoleId(), assignment.getPermissionId());
            return ResponseEntity.ok(convertToDto(role));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/permissions/remove")
    @Operation(summary = "Remove permission from role", description = "Remove a permission from a role")
    @PreAuthorize("hasAuthority('ROLE_ROLE_UPDATE')")
    public ResponseEntity<RoleDto> removePermissionFromRole(@RequestBody @Valid RolePermissionAssignmentDto assignment) {
        try {
            RoleEntity role = roleService.removePermissionFromRole(assignment.getRoleId(), assignment.getPermissionId());
            return ResponseEntity.ok(convertToDto(role));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}/activate")
    @Operation(summary = "Activate role", description = "Activate a deactivated role")
    @PreAuthorize("hasAuthority('ROLE_ROLE_UPDATE')")
    public ResponseEntity<RoleDto> activateRole(@PathVariable UUID id) {
        try {
            RoleEntity role = roleService.activateRole(id);
            return ResponseEntity.ok(convertToDto(role));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @PutMapping("/{id}/deactivate")
    @Operation(summary = "Deactivate role", description = "Deactivate a role (soft delete)")
    @PreAuthorize("hasAuthority('ROLE_ROLE_UPDATE')")
    public ResponseEntity<RoleDto> deactivateRole(@PathVariable UUID id) {
        try {
            RoleEntity role = roleService.deactivateRole(id);
            return ResponseEntity.ok(convertToDto(role));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    private RoleDto convertToDto(RoleEntity role) {
        RoleDto dto = new RoleDto();
        dto.setId(role.getId());
        dto.setName(role.getName());
        dto.setDescription(role.getDescription());
        dto.setActive(role.isActive());
        
        Set<PermissionDto> permissionDtos = role.getPermissions().stream()
                .map(this::convertPermissionToDto)
                .collect(Collectors.toSet());
        dto.setPermissions(permissionDtos);
        
        return dto;
    }

    private PermissionDto convertPermissionToDto(Permission permission) {
        return new PermissionDto(
                permission.getId(),
                permission.getName(),
                permission.getDescription(),
                permission.getCategory(),
                permission.isActive()
        );
    }
}
