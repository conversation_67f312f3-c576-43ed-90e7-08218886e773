package com.gofar.user_service.repository;

import com.gofar.user_service.entity.Permission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface PermissionRepository extends JpaRepository<Permission, UUID> {

    /**
     * Find permission by name
     */
    Optional<Permission> findByName(String name);

    /**
     * Find all active permissions
     */
    List<Permission> findByActiveTrue();

    /**
     * Find permissions by category
     */
    List<Permission> findByCategoryAndActiveTrue(String category);

    /**
     * Check if permission exists by name
     */
    boolean existsByName(String name);

    /**
     * Find all distinct categories
     */
    @Query("SELECT DISTINCT p.category FROM Permission p WHERE p.active = true ORDER BY p.category")
    List<String> findDistinctCategories();

    /**
     * Find permissions by name containing (case-insensitive)
     */
    @Query("SELECT p FROM Permission p WHERE LOWER(p.name) LIKE LOWER(CONCAT('%', :name, '%')) AND p.active = true")
    List<Permission> findByNameContainingIgnoreCase(@Param("name") String name);
}
