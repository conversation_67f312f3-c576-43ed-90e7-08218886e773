package com.gofar.user_service.config;

import com.gofar.user_service.dto.EcoUserCreationDto;
import com.gofar.user_service.service.EcoUserService;
import com.gofar.user_service.service.PermissionService;
import com.gofar.user_service.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

import static com.gofar.user_service.utils.Constants.*;


@Configuration
public class DataInitializer {

    private PermissionService permissionService;
    private RoleService roleService;
    private EcoUserService userService;

    @Bean
    @Transactional
    ApplicationRunner initializeData() {
        return args -> {
            createPermissionsIfNotExist();
            createRolesIfNotExist();
            createDefaultAdminUser();
        };
    }

    /**
     * Create all permissions if they do not exist
     */
    private void createPermissionsIfNotExist() {
        for(Map.Entry<String, Map<String, String>> entry : PERMISSION_DESCRIPTIONS.entrySet()) {
            for(Map.Entry<String, String> permission : entry.getValue().entrySet()) {
                createPermissionIfNotExists(permission.getKey(), permission.getValue(), entry.getKey());
            }
        }
    }

    private void createRolesIfNotExist() {
        for(Map.Entry<String, String> entry : ROLE_DESCRIPTIONS.entrySet()) {
            if (roleService.findByName(entry.getKey()).isEmpty()) {
                roleService.createRole(entry.getKey(), entry.getValue());
                setPermissions(entry.getKey());
            }
        }
    }

    private void createDefaultAdminUser() {
        if (userService.findByEmail(DEFAULT_ADMIN_EMAIL).isEmpty()) {
            userService.save(new EcoUserCreationDto(DEFAULT_ADMIN_EMAIL, DEFAULT_ADMIN_PASSWORD, "Admin", "Admin", "ADMIN", null));
        }
    }

    private void createPermissionIfNotExists(String name, String description, String category) {
        if (permissionService.findByName(name).isEmpty()) {
            permissionService.createPermission(name, description, category);
        }
    }

    private void addPermissionToRole(String roleName, String permissionName) {
        try {
            roleService.addPermissionToRole(roleName, permissionName);
        } catch (IllegalArgumentException e) {
            // Permission might already be assigned, ignore
        }
    }

    /**
     * Initialize a role with the configured permissions
     *
     * @param roleName the name of the role to initialize
     */
    private void setPermissions(String roleName) {
        if (!ROLE_PERMISSIONS.containsKey(roleName)) {
            return;
        }

        for (String permission : ROLE_PERMISSIONS.get(roleName)) {
            addPermissionToRole(roleName, permission);
        }
    }

    @Autowired
    public void setPermissionService(PermissionService permissionService) {
        this.permissionService = permissionService;
    }

    @Autowired
    public void setRoleService(RoleService roleService) {
        this.roleService = roleService;
    }

    @Autowired
    public void setUserService(EcoUserService userService) {
        this.userService = userService;
    }
}
