package com.gofar.user_service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Permission data transfer object")
public class PermissionDto {

    @Schema(description = "Permission unique identifier", example = "123e4567-e89b-12d3-a456-************")
    private UUID id;

    @NotBlank(message = "Permission name is mandatory")
    @Size(max = 100, message = "Permission name must not exceed 100 characters")
    @Schema(description = "Permission name", example = "USER_READ")
    private String name;

    @Size(max = 255, message = "Permission description must not exceed 255 characters")
    @Schema(description = "Permission description", example = "Allows reading user information")
    private String description;

    @Size(max = 50, message = "Permission category must not exceed 50 characters")
    @Schema(description = "Permission category", example = "USER_MANAGEMENT")
    private String category;

    @Schema(description = "Whether the permission is active", example = "true")
    private boolean active = true;

    public PermissionDto(String name, String description, String category) {
        this.name = name;
        this.description = description;
        this.category = category;
    }
}
