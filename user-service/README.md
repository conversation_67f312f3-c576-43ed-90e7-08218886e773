# User Service

The User Service is a microservice component of the Eco Home Application that handles user management, authentication, and profile operations. It provides secure user registration, authentication validation, and profile management capabilities with energy consumption preferences.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Architecture](#architecture)
- [Technology Stack](#technology-stack)
- [Getting Started](#getting-started)
- [API Endpoints](#api-endpoints)
- [Security](#security)
- [Database Schema](#database-schema)
- [Configuration](#configuration)
- [Development](#development)

## Overview

The User Service is built as a Spring Boot microservice that integrates with the Eco Home Application ecosystem. It manages user accounts with role-based access control and energy consumption preferences, supporting the application's eco-friendly energy management goals.

## Features

- **User Registration**: Create new user accounts with validation
- **User Authentication**: Validate user credentials for authentication
- **Profile Management**: Update user profiles and preferences
- **Role-Based Access Control**: Support for ADMIN, USER, and TECHNICIAN roles
- **Energy Preferences**: Manage eco-friendly energy consumption settings
- **Data Encryption**: Email addresses are encrypted in the database
- **OAuth2 Integration**: JWT-based authentication with OAuth2 resource server
- **Service Discovery**: Eureka client for microservice registration
- **API Documentation**: OpenAPI/Swagger documentation

## Architecture

The service follows a layered architecture pattern:

```
├── Controller Layer (REST endpoints)
├── Service Layer (Business logic)
├── Repository Layer (Data access)
└── Entity Layer (Data models)
```

### Key Components

- **EcoUserController**: REST API endpoints
- **EcoUserService**: Business logic and user operations
- **EcoUserRepository**: JPA repository for database operations
- **EcoUser Entity**: User data model with encrypted email
- **EnergyPreference**: Embedded entity for energy consumption preferences
- **Security Configuration**: OAuth2 resource server setup

## Technology Stack

- **Java 21**
- **Spring Boot 3.5.3**
- **Spring Security** (OAuth2 Resource Server)
- **Spring Data JPA**
- **Spring Cloud Config**
- **Spring Cloud Netflix Eureka**
- **PostgreSQL** (Database)
- **Lombok** (Code generation)
- **SpringDoc OpenAPI** (API documentation)
- **BCrypt** (Password encryption)
- **AES** (Email encryption)

## Getting Started

### Prerequisites

- Java 21 or higher
- Maven 3.6+
- PostgreSQL database
- Config Server running on port 8888
- Eureka Server running on port 8761
- Auth Server running on port 9000

### Installation

1. Clone the repository
2. Navigate to the user-service directory
3. Configure the database connection in `application.yml`
4. Run the application:

```bash
mvn spring-boot:run
```

The service will start on the default port and register with Eureka.

### Database Setup

Create a PostgreSQL database named `eco_users`:

```sql
CREATE DATABASE eco_users;
```

The application will automatically create the required tables using JPA/Hibernate.

## API Endpoints

### Base URL
```
http://localhost:{port}/api/v1/users
```

### Endpoints

| Method | Endpoint    | Description               | Authentication |
|--------|-------------|---------------------------|----------------|
| `GET`  | `/{email}`  | Find user by email        | Required (JWT) |
| `POST` | `/`         | Create new user           | Admin only     |
| `POST` | `/validate` | Validate user credentials | Public         |

### API Documentation

Access the interactive API documentation at:
- Swagger UI: `http://localhost:{port}/swagger-ui.html`
- OpenAPI JSON: `http://localhost:{port}/v3/api-docs`

## Security

### Authentication & Authorization

- **OAuth2 Resource Server**: JWT token validation
- **Role-Based Access**: ADMIN, USER, TECHNICIAN roles
- **Scope-Based Authorization**: Different scopes for different roles
- **Password Encryption**: BCrypt hashing
- **Email Encryption**: AES encryption for PII protection

### Security Configuration

- Admin scope required for user creation
- JWT authentication for protected endpoints
- Public access for user validation and documentation
- CORS and CSRF disabled for API access

### User Roles & Scopes

| Role       | Scopes      |
|------------|-------------|
| ADMIN      | admin, user |
| USER       | user        |
| TECHNICIAN | technician  |

## Database Schema

### EcoUser Table (ECUSR)

| Column   | Type    | Description                       |
|----------|---------|-----------------------------------|
| id       | UUID    | Primary key                       |
| USRML    | VARCHAR | Encrypted email (unique)          |
| USRPWD   | VARCHAR | BCrypt hashed password            |
| role     | VARCHAR | User role (ADMIN/USER/TECHNICIAN) |
| USRFNM   | VARCHAR | First name                        |
| USRLNM   | VARCHAR | Last name                         |
| USRPFEC  | BOOLEAN | Priority eco preference           |
| USRPFEVC | BOOLEAN | EV charging preference            |
| USRPFTM  | VARCHAR | Preferred time slot               |

### Energy Preferences

Embedded within the user entity:
- **priorityEco**: Boolean flag for eco-friendly priority
- **EVCharging**: Boolean flag for electric vehicle charging
- **preferredTime**: Enum for preferred energy consumption time slots
  - EARLY_MORNING (06:00-08:00)
  - MORNING (08:00-12:00)
  - AFTERNOON (12:00-16:00)
  - EVENING (16:00-20:00)
  - NIGHT (20:00-23:00)

## Configuration

### Application Properties

Key configuration properties in `application.yml`:

```yaml
spring:
  application:
    name: user-service
  datasource:
    url: ******************************************
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: "http://localhost:9000"
eureka:
  client:
    serviceUrl:
      defaultZone: http://localhost:8761/eureka/
```

### Environment Variables

- `CONFIG_SERVER_USER_NAME`: Config server username (default: admin)
- `CONFIG_SERVER_PASSWORD`: Config server password (default: 123456)

## Development

### Running Tests

```bash
mvn test
```

### Building the Application

```bash
mvn clean package
```

### Default Admin User

The application automatically creates a default admin user on startup:
- Email: <EMAIL>
- Password: admin
- Role: ADMIN

### Code Structure

```
src/main/java/com/gofar/user_service/
├── controller/          # REST controllers
├── service/            # Business logic
├── repository/         # Data access layer
├── entity/            # JPA entities
├── dto/               # Data transfer objects
├── security/          # Security configuration
└── config/            # Application configuration
```

## Contact

- **Developer**: Gofar
- **Email**: <EMAIL>
- **Website**: https://echo_home.com

---

For more information about the complete Eco Home Application ecosystem, refer to the main project documentation.
