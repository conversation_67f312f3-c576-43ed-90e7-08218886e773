package com.gofar.user_service.service;

import com.gofar.user_service.dto.*;
import com.gofar.user_service.entity.EcoUser;
import com.gofar.user_service.entity.EnergyPreference;
import com.gofar.user_service.repository.EcoUserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class EcoUserService {

    private final EcoUserRepository ecoUserRepository;
    private PasswordEncoder passwordEncoder;

    public EcoUserService(EcoUserRepository ecoUserRepository) {
        this.ecoUserRepository = ecoUserRepository;
    }

    public Optional<EcoUserResponseDto> findByEmail(String email) {
        return ecoUserRepository.findByEmail(email)
                .map(user -> new EcoUserResponseDto(user.getId(),
                        user.getEmail(), user.getFirstName(), user.getLastName(), user.getRole(), user.getPreference()));
    }

    public EcoUserResponseDto save(EcoUserCreationDto ecoUserDto) {
        if (ecoUserRepository.existsByEmail(ecoUserDto.getEmail())) {
            throw new IllegalArgumentException("Email already exists");
        }
        EcoUser user = new EcoUser();
        user.setFirstName(ecoUserDto.getFirstName());
        user.setLastName(ecoUserDto.getLastName());
        user.setEmail(ecoUserDto.getEmail());
        user.setPassword(passwordEncoder.encode(ecoUserDto.getPassword()));
        user.setRole(ecoUserDto.getRole());
        user.setPreference(ecoUserDto.getPreference());
        ecoUserRepository.save(user);
        return new EcoUserResponseDto(user.getId(),
                user.getEmail(), user.getFirstName(), user.getLastName(), user.getRole(), user.getPreference());
    }

    public ValidEcoUserDetails validateUser(String email, String password) {
        Optional<EcoUser> userOptional = ecoUserRepository.findByEmail(email);
        if (userOptional.isPresent() && passwordEncoder.matches(password, userOptional.get().getPassword())) {
            EcoUser user = userOptional.get();
            List<String> scopes = switch (user.getRole()) {
                case ADMIN -> new ArrayList<>(List.of("admin", "user"));
                case USER -> new ArrayList<>(List.of("user"));
                case TECHNICIAN -> new ArrayList<>(List.of("technician"));
            };
            return new ValidEcoUserDetails(user.getEmail(), user.getRole(), scopes);
        }
        return null;
    }

    public void updateProfile(ProfileUpdateDto updateDto) {
        EcoUser currentUser = getCurrentUser();
        currentUser.setFirstName(updateDto.firstName());
        currentUser.setLastName(updateDto.lastName());
        ecoUserRepository.saveAndFlush(currentUser);
    }

    public void updatePassword(String oldPassword, String newPassword) {
        EcoUser currentUser = getCurrentUser();
        if (passwordEncoder.matches(oldPassword, currentUser.getPassword())) {
            currentUser.setPassword(passwordEncoder.encode(newPassword));
            ecoUserRepository.saveAndFlush(currentUser);
        }
        throw new IllegalArgumentException("Invalid old password");
    }

    public void updatePreferences(PreferencesDto preferencesDto) {
        EcoUser currentUser = getCurrentUser();
        currentUser.setPreference(new EnergyPreference(preferencesDto.priorityEco(), preferencesDto.EVCharging(), preferencesDto.preferredTime()));
        ecoUserRepository.saveAndFlush(currentUser);
    }


    private EcoUser getCurrentUser() {
        return ecoUserRepository.findByEmail(getUserEmail()).orElseThrow(() -> new IllegalArgumentException("User not found"));
    }

    private String getUserEmail() {
        return SecurityContextHolder.getContext().getAuthentication().getName();
    }

    @Autowired
    public void setPasswordEncoder(PasswordEncoder passwordEncoder) {
        this.passwordEncoder = passwordEncoder;
    }
}
